import React from "react";
import Image from "next/image";
import { Agent } from "../../page";

const AgentsTop = ({
  agentsSearch,
  setAgentsSearch,
  handleAgentsSearch,
  initialAgents,
}: {
  agentsSearch: string;
  setAgentsSearch: React.Dispatch<React.SetStateAction<string>>;
  handleAgentsSearch: any;
  initialAgents: Agent[];
}) => {
  return (
    <div className="flex items-center justify-center">
      <div className="flex flex-col md:flex-row gap-3 pt-10 px-2 md:px-6 lg:px-8 items-center justify-between text-center w-full bg-white xl:max-w-[1550px]">
        <p className="font-semibold">{initialAgents.length} Agents - At Your Service</p>

        <form
          className="flex gap-3 border border-[#E6EAEF] rounded-md h-10 px-2 bg-white w-[300px]"
          onSubmit={handleAgentsSearch}
        >
          <Image
            src={"/images/search-icon.svg"}
            alt="Search Icon"
            width={20}
            height={20}
          />
          <input
            type="text"
            placeholder="Find an Agent"
            className="outline-none border-none rounded-md w-full text-sm"
            value={agentsSearch}
            onChange={(e) => setAgentsSearch(e.target.value)}
          />
        </form>
      </div>
    </div>
  );
};

export default AgentsTop;
