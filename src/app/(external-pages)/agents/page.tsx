"use client";

import React, { useEffect, useState } from "react";
// import Link from "next/link";
import AgentsHero from "./components/agents-hero";
import AgentsTop from "./components/agents-top";
import HorizontalCarousel from "./components/agents-carousel";
import AgentsContent from "./components/agents-content";
import { agents } from "./data/agents";
import AgentsContact from "./components/agents-contact";
import Image from "next/image";

const Agents = () => {
  const [agentsSearch, setAgentsSearch] = useState("");
  const [agentsCategory, setAgentsCategory] = useState("All");
  const [agentsDisplayed, setAgentsDisplayed] = useState([...agents]);
  const [openDialog, setOpenDialog] = useState(false);

  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    monitoringNeed: "",
  });

  const isFormValid =
    form.firstName && form.lastName && form.email && form.monitoringNeed;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const handleAgentsSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setAgentsSearch("");
  };

  const handleAgentsChange = () => {
    if (agentsCategory !== "All") {
      setAgentsDisplayed(
        agents.filter((item) => item.section === agentsCategory)
      );
    } else {
      setAgentsDisplayed(agents);
    }

    if (agentsSearch !== "") {
      setAgentsDisplayed(
        agents.filter((item) =>
          item.title.toLowerCase().includes(agentsSearch.toLowerCase())
        )
      );
    }
  };

  useEffect(() => {
    handleAgentsChange();
  }, [agentsCategory, agentsSearch]);

  useEffect(() => {
    const fetchAgents = async () => {
      try {
        const response = await fetch('https://api.staging.telex.im/api/v1/agents');
        const data = await response.json();
        if (response.ok) {
          setAgentsDisplayed(data.data);
        } else {
          console.error('Failed to fetch agents:', data);
        }
      } catch (error) {
        console.error('Error fetching agents:', error);
      }
    };

    fetchAgents();
  }, []);

  return (
    <div className="bg-white relative">
      <AgentsHero
        agentsSearch={agentsSearch}
        handleAgentsSearch={handleAgentsSearch}
        setAgentsSearch={setAgentsSearch}
      />
      <div className="space-y-5">
        <AgentsTop
          agentsSearch={agentsSearch}
          handleAgentsSearch={handleAgentsSearch}
          setAgentsSearch={setAgentsSearch}
        />
        <HorizontalCarousel setAgentsCategory={setAgentsCategory} />
      </div>
      <AgentsContent
        agentsDisplayed={agentsDisplayed}
        setOpenDialog={setOpenDialog}
      />
      <AgentsContact />
      {openDialog && (
        <div className="fixed top-0 right-0 left-0 bottom-0 flex items-center backdrop-blur-sm justify-center max-h-screen z-50">
          <div className="w-[90%] md:w-[60%] md:h-[70%]">
            <div className="flex border border-blue-800 rounded-lg w-full h-full bg-white ">
              <div className="rounded-lg hidden md:flex w-2/5">
                <Image
                  src={"/images/agents-request-image.svg"}
                  height={607}
                  width={422}
                  className="rounded-lg object-cover w-full h-full"
                  alt="Request Agent Image"
                />
              </div>
              <div className="rounded-lg w-full md:w-3/5">
                <form action="" className="p-5 space-y-6">
                  <div className="">
                    <h2 className="font-bold text-xl">Request an Agent</h2>
                    <p className="text-sm text-[#475467]">
                      {`Can't find an agent you need? Feel free to shoot us a
                      request and we will try to bring them on.`}
                    </p>
                  </div>
                  <div className="flex gap-4">
                    <div className="flex flex-col text-sm w-1/2 gap-2">
                      <label htmlFor="firstName">First Name</label>
                      <input
                        type="text"
                        placeholder="John"
                        className="text-[#475467] border outline-none rounded-md py-3 px-4 w-full"
                        onChange={handleChange}
                        value={form.firstName}
                        name="firstName"
                      />
                    </div>
                    <div className="flex flex-col text-sm w-1/2 gap-2">
                      <label htmlFor="lastName">Last Name</label>
                      <input
                        type="text"
                        placeholder="Doe"
                        className="text-[#475467] border outline-none rounded-md py-3 px-4 w-full"
                        onChange={handleChange}
                        value={form.lastName}
                        name="lastName"
                      />
                    </div>
                  </div>
                  <div className="flex flex-col text-sm gap-2">
                    <label htmlFor="email">Email</label>
                    <input
                      type="email"
                      placeholder="<EMAIL>"
                      className="text-[#475467] border outline-none rounded-md py-3 px-4"
                      onChange={handleChange}
                      value={form.email}
                      name="email"
                    />
                  </div>
                  <div className="flex flex-col text-sm gap-2">
                    <label htmlFor="monitoringNeed">Monitoring Need</label>
                    <input
                      type="text"
                      placeholder="E.g. I need an agent to monitor API response times"
                      maxLength={80}
                      max={80}
                      className=" border outline-none rounded-md py-3 px-4"
                      onChange={handleChange}
                      value={form.monitoringNeed}
                      name="monitoringNeed"
                    />
                  </div>
                  <div className="flex items-center gap-4 justify-end text-sm">
                    <button
                      onClick={() => setOpenDialog(false)}
                      className="border px-4 py-3 rounded-md"
                    >
                      Cancel
                    </button>
                    <button
                      disabled={!isFormValid}
                      className={`px-4 py-3 border rounded-md ${!isFormValid ? "opacity-60 cursor-not-allowed" : "opacity-100"}`}
                    >
                      Send Request
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Agents;
