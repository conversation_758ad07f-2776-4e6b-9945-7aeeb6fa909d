import React from "react";
import Image from "next/image";

const IndustryHeader = ({ imag }: any) => {
  return (
    <div className="flex flex-col items-center justify-center gap-5 lg:gap-2 pb-10 sm:pb-20 border-b">
      <div className="flex flex-col col-reverse items-center justify-center md:h-[327px] w-full lg:w-[954px] py-8 sm:py-20 gap-4">
        {/* <p className="text-left text-sm text-primary-500 mb-2">Industry</p> */}
        <h1 className="font-semibold text-3xl leading-[45px] sm:text-4xl sm:leading-[50px] md:text-[40px] md:leading-[50px] lg:text-5xl lg:leading-[60px] text-center">
          Telex:<span className="text-primary-500"> For Every Industry</span>
        </h1>

        <p className="text-base sm:text-lg text-[#808080] my-5 text-center w-full md:w-[80%] lg:w-[90%] lg:leading-[30px]">
          From banking to e-commerce, healthcare to travel—businesses run on
          uptime, data, and
          <span className="lg:block">
            {" "}
            automation. Telex ensures your critical systems stay operational,
            your alerts are real-time, and your{" "}
          </span>
          insights are always actionable.
        </p>
        {/* <div className="flex gap-4 items-center justify-center">
          <Link href={"/auth/sign-up"}>
            <Button className="bg-black bg-gradient-to-b from-[#8760f8] to-[#7141f8] text-[#fff] py-3 sm:py-6 sm:px-4 hover:scale-110 rounded-md transition-all duration-500">
              Start Monitoring
            </Button>
          </Link>
          <Link href={"/agents"}>
            <Button className="border border-primary-500 !bg-white   text-primary-500 py-3 sm:py-6 sm:px-4 hover:scale-110 rounded-md transition-all duration-500">
              Meet the Agents
            </Button>
          </Link>
        </div> */}
      </div>

      <div className=" overflow-hidden rounded-3xl bg-gray-100 h-[400px] sm:h-[628px] w-full border">
        <Image src={imag} alt="" className="h-full w-full object-cover" />
      </div>
    </div>
  );
};

export default IndustryHeader;
