import { <PERSON><PERSON><PERSON> } from "lucide-react";
import Link from "next/link";
import React from "react";
import { <PERSON>ton } from "~/components/ui/button";
import Image from "next/image";

const Industries = () => {
  return (
    <section className="py-10 md:py-20 px-4 md:px-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col justify-center gap-5 lg:gap-4 pb-10 sm:pb-20">
          <div className="flex items-center justify-center gap-2 lg:gap-2 border-[#F1F1FE] rounded-[50px] bg-gradient-to-b from-white via-white to-[#F2EFFA] px-2 py-2 border-2 w-[100px] sm:w-[120px] lg:w-[109px] h-[32px] sm:h-[36px] ">
            <Image
              src="/images/industry-wink.svg"
              alt="e-commerce"
              width={16}
              height={16}
              className="object-cover"
            />
            <p className="text-left text-xs sm:text-sm   text-[#4025AD] font-medium">
              Solutions
            </p>
          </div>
          <h1 className="font-semibold text-2xl md:text-3xl lg:text-[36px] lg:leading-[45px] lg:mr-[300px]">
            Industry Challenges, Solved by AI-Powered Monitoring
          </h1>
          <p className="text-[#344054] text-base lg:w-[650px] xl:w-[948px]">
            Different industries have different challenges—but one thing they
            all need is reliability. Telex helps you stay ahead of downtime,
            errors, and bottlenecks, no matter your field.
          </p>

          <Link href="/auth/sign-up" className="block">
            <Button className="bg-black bg-gradient-to-b from-[#8760f8] to-[#7141f8] text-white py-4 px-6 sm:py-5 sm:px-8 md:py-6 md:px-10  sm:w-auto flex gap-2 items-center justify-center text-sm sm:text-base md:text-lg rounded-lg">
              Start Free Trial <ArrowRight />
            </Button>
          </Link>
        </div>

        <div className="grid md:grid-cols-2 gap-4 lg:gap-6">
          {/* First Card */}
          <div className="rounded-xl border shadow-md transition-all duration-500 hover:scale-100 hover:shadow-lg bg-[#F9FAFB] border-[#E6EAEF]">
            <div className="flex flex-row items-start sm:items-center justify-between gap-4 p-4 sm:p-5 border-b-2">
              <div className="flex items-center gap-3 font-semibold text-base sm:text-lg">
                <Image
                  src="/images/industry-ecommerce.png"
                  alt="e-commerce"
                  width={48}
                  height={48}
                  className="object-cover"
                />
                <h1 className="text-sm lg:text-lg">E-Commerce</h1>
              </div>
              <Link href={"/industry/e-commerce"}>
                <Button className="border border-primary-500  text-primary-500 py-2 px-4 sm:py-3 sm:px-6 text-sm sm:text-base hover:bg-primary-500 hover:text-white rounded-md transition-all duration-500">
                  Learn More
                </Button>
              </Link>
            </div>

            <div className="flex flex-col justify-between p-5 gap-4">
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Automate customer support, lead nurturing, and personalized
                  Al-powered sales experiences.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Manage invoices, product documentation, and compliance
                  materials with ease.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Maintain uptime with site reliability monitoring and site
                  security alerts.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Extract, analyze, and route product and customer data for
                  better campaign targeting.
                </p>
              </div>
            </div>
          </div>

          {/* Second Card */}
          <div className="rounded-xl shadow-md border  transition-all duration-500 hover:scale-100 hover:shadow-lg bg-[#F9FAFB] border-[#E6EAEF]">
            <div className="flex flex-row items-start sm:items-center justify-between gap-4 p-4 sm:p-5 border-b-2">
              <div className="flex items-center gap-3 font-semibold sm:text-lg">
                <Image
                  src="/images/industry-health.png"
                  alt="e-commerce"
                  width={48}
                  height={48}
                  className="object-cover"
                />
                <h1 className="text-sm lg:text-lg">HealthCare</h1>
              </div>
              <Link href={"/industry/healthcare"}>
                <Button className="border border-primary-500  text-primary-500 py-2 px-4 sm:py-3 sm:px-6 text-sm sm:text-base hover:bg-primary-500 hover:text-white rounded-md transition-all duration-500">
                  Learn More
                </Button>
              </Link>
            </div>
            <div className="flex flex-col justify-between p-5 gap-4">
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Monitor EMR uptime and automate IT operations for
                  uninterrupted care.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Streamline document creation and management for medical
                  records.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Use Al agents to assist with appointment scheduling and
                  patient support.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Secure sensitive data with proactive site security and system
                  monitoring.
                </p>
              </div>
            </div>
          </div>

          {/* Third Card */}
          <div className="rounded-xl shadow-md transition-all duration-500 hover:scale-100 hover:shadow-lg border bg-[#F9FAFB] border-[#E6EAEF]">
            <div className="flex flex-row items-start sm:items-center justify-between gap-4 p-4 sm:p-5 border-b-2">
              <div className="flex items-center gap-3 font-semibold sm:text-lg">
                <Image
                  src="/images/industry-bank.png"
                  alt="e-commerce"
                  width={48}
                  height={48}
                  className="object-cover"
                />
                <h1 className="text-sm lg:text-lg">Banking and Finance</h1>
              </div>
              <Link href={"/industry/finance"}>
                <Button className="border border-primary-500  text-primary-500 py-2 px-4 sm:py-3 sm:px-6 text-sm sm:text-base hover:bg-primary-500 hover:text-white rounded-md transition-all duration-500">
                  Learn More
                </Button>
              </Link>
            </div>
            <div className="flex flex-col justify-between p-5 gap-4">
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Protect sensitive information with Al-powered document
                  management and site security workflows.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>Automate invoice processing and documentation workflows.</p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Ensure DevOps reliability for real-time transaction
                  monitoring.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Strengthen customer relationships with responsive Al support
                  agents.
                </p>
              </div>
            </div>
          </div>

          {/* Fourth Card */}
          <div className="rounded-xl shadow-md transition-all duration-500 hover:scale-100 hover:shadow-lg border bg-[#F9FAFB] border-[#E6EAEF]">
            <div className="flex flex-row items-start sm:items-center justify-between gap-4 p-4 sm:p-5 border-b-2">
              <div className="flex items-center gap-3 font-semibold sm:text-lg">
                <Image
                  src="/images/industry-travel.png"
                  alt="e-commerce"
                  width={48}
                  height={48}
                  className="object-cover"
                />
                <h1 className="text-sm lg:text-lg">Travel & Hospitality</h1>
              </div>
              <Link href={"/industry/travel-hospitality"}>
                <Button className="border border-primary-500  text-primary-500 py-2 px-4 sm:py-3 sm:px-6 text-sm sm:text-base hover:bg-primary-500 hover:text-white rounded-md transition-all duration-500">
                  Learn More
                </Button>
              </Link>
            </div>
            <div className="flex flex-col justify-between p-5 gap-4">
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Use Al to automate bookings, route updates, and customer
                  interactions.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Manage marketing campaigns, travel documents, and site content
                  from one hub.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Enhance uptime for reservation systems with site reliability
                  monitoring.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>Analyze booking trends to personalize offers and content.</p>
              </div>
            </div>
          </div>

          {/* Fifth Card */}
          <div className="rounded-xl shadow-md transition-all duration-500 hover:scale-100 hover:shadow-lg border bg-[#F9FAFB] border-[#E6EAEF]">
            <div className="flex flex-row items-start sm:items-center justify-between gap-4 p-4 sm:p-5 border-b-2">
              <div className="flex items-center gap-3 font-semibold sm:text-lg">
                <Image
                  src="/images/industry-tech.png"
                  alt="e-commerce"
                  width={48}
                  height={48}
                  className="object-cover"
                />
                <h1 className="text-sm lg:text-lg">Saas & Tech</h1>
              </div>
              <Button className="border border-primary-500  text-primary-500 py-2 px-4 sm:py-3 sm:px-6 text-sm sm:text-base hover:bg-primary-500 hover:text-white rounded-md transition-all duration-500">
                Learn More
              </Button>
            </div>
            <div className="flex flex-col justify-between p-5 gap-4">
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Ensure app uptime with DevOps observability and incident
                  alerts.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Use Al for lead generation, support automation, and user
                  onboarding.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Power documentation, video walkthroughs, and content creation.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Streamline internal operations with automated workflows and
                  secure systems.
                </p>
              </div>
            </div>
          </div>

          {/* Sixth Card */}
          <div className="rounded-xl shadow-md transition-all duration-500 hover:scale-100 hover:shadow-lg border bg-[#F9FAFB] border-[#E6EAEF]">
            <div className="flex flex-row items-start sm:items-center justify-between gap-4 p-4 sm:p-5 border-b-2">
              <div className="flex items-center gap-3 font-semibold sm:text-lg">
                <Image
                  src="/images/industry-logistics.png"
                  alt="e-commerce"
                  width={48}
                  height={48}
                  className="object-cover"
                />
                <h1 className="text-sm lg:text-lg">Logistics & Supply Chain</h1>
              </div>
              <Link href={"/industry/logistics"}>
                <Button className="border border-primary-500  text-primary-500 py-2 px-4 sm:py-3 sm:px-6 text-sm sm:text-base hover:bg-primary-500 hover:text-white rounded-md transition-all duration-500">
                  Learn More
                </Button>
              </Link>
            </div>
            <div className="flex flex-col justify-between p-5 gap-4">
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Monitor fleet tracking and warehouse systems for downtime.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Process and manage invoices, delivery documents, and
                  compliance records.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Use Al to automate customer notifications and support
                  inquiries.
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Image
                  src="/images/industry-star.svg"
                  alt="star"
                  width={24}
                  height={24}
                  className="object-cover"
                />
                <p>
                  Detect disruptions, generate reports, and ensure inventory
                  visibility at all times.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Industries;
