import React from "react";
import Link from "next/link";
import { But<PERSON> } from "~/components/ui/button";
import Image from "next/image";

const IndustryReady = () => {
  return (
    <div className='relative bg-[url("/images/industry-ready-bg.jpeg")] bg-cover bg-no-repeat bg-center w-full'>
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-[linear-gradient(to_bottom_right,_rgba(0,0,0,0.8)_0%,_rgba(0,0,0,0.3)_30%,_rgba(0,0,0,0)_100%)] z-0"></div>

      {/* Main Content */}
      <div className="relative z-10 flex items-end pt-8 lg:pt-24 px-4 sm:px-16 lg:px-0">
        <div className="w-full max-w-screen-2xl flex flex-col lg:flex-row items-center lg:items-start justify-center lg:justify-between gap-12 pl-0 sm:pl-[40px] xl:pl-[130px] mx-auto ">
          {/* Text Content */}
          <div className="w-full lg:w-[484px] max-w-[484px] text-center lg:text-left flex flex-col gap-6">
            <h1 className="text-white text-2xl sm:text-4xl font-semibold leading-tight">
              Ready to Automate Workflows Across Your Industry?
            </h1>
            <p className="text-white text-base lg:text-base">
              Let Telex agents handle the repetitive tasks, from approvals to
              content prep and invoice routing, so your team can focus on
              strategy, not busywork.
            </p>
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-6 justify-center lg:justify-start">
              <Link href="/auth/sign-up">
                <Button className="bg-gradient-to-b from-[#8760f8] to-[#7141f8] text-white py-4 px-6  text-sm sm:text-base  hover:scale-110 rounded-md transition-all duration-500">
                  Sign Up
                </Button>
              </Link>
              <Link href="/agents">
                <Button className="border border-primary-500 !bg-white text-primary-500 py-4 px-6  text-sm sm:text-base hover:scale-110 rounded-md transition-all duration-500">
                  Browse Agents
                </Button>
              </Link>
            </div>
          </div>

          {/* Image */}
          <div className="w-full max-w-[500px] sm:max-w-[600px] lg:max-w-[816px]">
            <Image
              src="/images/industry-ready-view.svg"
              alt="star"
              width={816}
              height={620}
              className="object-contain w-full h-auto rounded-t-[10px] lg:rounded-none sm:rounded-tl-[10px]"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default IndustryReady;
