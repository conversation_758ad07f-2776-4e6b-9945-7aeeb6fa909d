"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Video Generation - Telex",
    description:
      "Create compelling video content with AI-powered tools. Transform text, images, and ideas into engaging video content effortlessly with professional quality.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/case-image-one.svg";
import CaseTwo from "../_assets/case-image-two.svg";
import CardSlider from "../components/single-product/CardSlider";
const firstData = [
  {
    id: 1,
    title: "Script Creation from Text",
    content: `Describe your idea or upload an existing document, notes or rough script.`,
  },
  {
    id: 2,
    title: "Write and Edit Video with Telex All",
    content: `Telex Al agent handles everything from scriptwriting to voiceover and scene transitions.`,
  },
  {
    id: 3,
    title: "Choose your style",
    content: `Select the format that fits your goal, whether it's for education, marketing, or internal use.`,
  },
  {
    id: 4,
    title: "Review and export",
    content: `Watch a preview, tweak the script or visuals, and export the final video in your preferred format.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex helped a fintech marketer, boost product understanding",
    image: CaseOne?.src,
    content: `Telex generated 3 product explainer videos in one day with user preferred avatar and audio tone.`,
  },
  {
    id: 2,
    title: "How Telex helped a solopreneur launch an online course",
    image: CaseTwo.src,
    content: `Telex turned notes into a series of professional training videos, no camera, editing, or studio needed making the course publishable under a week.`,
  },
  {
    id: 3,
    title: "How Telex helped a support team reduce ticket volume",
    image: CaseOne.src,
    content: `Telex created bite-sized how-to videos from FAQs using. The videos were embedded in the help center resulting in ticket volume drop.`,
  },
];

const technologies = [
  {
    id: 1,
    title: "Training Videos",
    content:
      "Convert text documents, guides, or onboarding checklists into clear, voice-narrated instructional videos that are easy to follow and reusable.",
    link: "/technology/application-monitoring/monitor-web-application",
  },
  {
    id: 2,
    title: "Product Demos",
    content:
      "Show how your product works by turning feature descriptions into clean, branded demo videos.",
    link: "/technology/application-monitoring/cloud-infrastructure",
  },
  {
    id: 3,
    title: "Marketing Clips",
    content:
      "Quickly create engaging short videos tailored for social media, ads, or landing pages with the right tone and visuals.",
    link: "/technology/application-monitoring/mobile-application",
  },
  {
    id: 4,
    title: "Custom Avatars & Voices",
    content:
      "Choose from a diverse range of Al-generated presenters and voices or upload your own for a personalized, on-brand feel.",
    link: "/technology/application-monitoring/enterprise-monitoring",
  },
  {
    id: 5,
    title: "Script Editing",
    content:
      "Easily adjust the video script before or after generation to fine-tune messaging, pacing, or clarity.",
    link: "/technology/application-monitoring/microservices",
  },
  {
    id: 6,
    title: "Agent to agent collaboration",
    content:
      "After your video is created, other Al agents can step in to auto-publishes to preferred socials, writes captions for socials, or launch a market campaign. This ensures a smooth content flow and light workload.",
    link: "/technology/application-monitoring/api-monitoring",
  },
];

const slides = [
  {
    title: "Set Up Telex",
    description: "Sign up on Telex and start creating videos",
    button: {
      text: "Sign up Now",
      href: "/auth/sign-up",
    },
  },
  {
    title: "Integrate your socials on Telex",
    description:
      "Connect your socials on Telex to post directly when your videos are ready",
  },
  {
    title: "Export and Publish anywhere",
    description:
      "Download or export videos to YouTube, embed in your site, or share on your preferred socials.",
  },
];

const videoGenerationFaq = [
  {
    id: 1,
    question: "What kind of videos can I create on Telex?",
    answer:
      "With Telex, you can create various types of videos including training videos, product demos, marketing clips, social media content, educational content, and promotional videos. Our AI-powered platform supports multiple video formats and styles to suit your specific needs.",
  },
  {
    id: 2,
    question: "Is there a limit on how many videos I can make?",
    answer:
      "The number of videos you can create depends on your subscription plan. We offer flexible plans that cater to different usage needs, from individual content creators to large enterprises requiring high-volume video production.",
  },
  {
    id: 3,
    question: "How do i integrate my socials on telex?",
    answer:
      "Integrating your social media accounts with Telex is simple. Navigate to the settings page, select 'Social Media Integration,' and follow the prompts to connect your preferred platforms. Once connected, you can directly publish your videos to these platforms from within Telex.",
  },
  {
    id: 4,
    question: "What voices and languages are available on Telex?",
    answer:
      "Telex offers a diverse range of AI-generated voices across multiple languages. We support major global languages and provide various voice options including different accents, ages, and tones. You can also customize the voice parameters to match your specific requirements.",
  },
  {
    id: 5,
    question: "Can I customize the visuals and audio tone?",
    answer:
      "Yes, Telex provides extensive customization options for both visuals and audio. You can adjust colors, fonts, and visual styles to match your brand, and fine-tune voice parameters such as speed, pitch, and emotional tone to achieve your desired presentation.",
  },
];

const VideoGeneration = () => {
  return (
    <>
      <Hero
        breadCumbs="Video generation"
        title="Turn {{ideas into videos}} in seconds"
        content="Quickly create training videos, product explainers, social clips, and more, all | powered by Al. No camera, no crew, just your creativity."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="From prompt to polished video, designed just for you."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`Whether explaining a concept, showcasing a product, or educating your audience, Telex helps you produce professional content fast, even if you've never edited a video before.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Quickly, easily generate high professional video regardless of your editing skills."
        items={technologies}
      />
      <div className="relative px-4 md:px-6 overflow-hidden">
        <div className="max-w-7xl mx-auto py-[60px]">
          <div className="flex justify-between items-center mb-10">
            <button className="px-4 py-1 text-sm font-semibold text-gray-900 border border-gray-400 bg-[#F9FAFB] rounded-full hover:bg-gray-100">
              <span className="text-gray-700">✩</span> <span>Guides</span>
            </button>
          </div>
          <CardSlider slides={slides} />
        </div>
      </div>
      <Faq faq={videoGenerationFaq} />
      <KeepTrack
        title="Your content, in motion without the production hassle with Telex video generation"
        content="Whether launching a new product, educating an audience, or simply saving time on content, Telex turns your words into compelling videos, quickly and easily."
      />
    </>
  );
};

export default VideoGeneration;
