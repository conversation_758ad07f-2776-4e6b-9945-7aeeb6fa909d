"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Data Extraction and Analysis - Telex",
    description:
      "Transform raw data into actionable business insights. Extract, process, and analyze data efficiently while ensuring accuracy and reliability.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/case-image-one.svg";
import CaseTwo from "../_assets/case-image-two.svg";

const firstData = [
  {
    id: 1,
    title: "Supply Chain and Inventory Tracking With Telex",
    content: `Pull data from suppliers, shipping partners, and inventory systems to gain real-time visibility into your supply chain and optimize stock levels.`,
  },
  {
    id: 2,
    title: "Monitor Compliance and Risk Management",
    content: `Extract and analyze regulatory data, legal documents, and compliance reports to ensure your business stays ahead of changing requirements and mitigates risks.`,
  },
  {
    id: 3,
    title: "Sales Intelligence and Lead Generation",
    content: `Gather contact information, company details, and buying signals from multiple platforms to build enriched lead lists and fuel targeted outreach campaigns.`,
  },
  {
    id: 4,
    title: "Financial Data Aggregation and Reporting",
    content: `Aggregate financial data from disparate sources for accurate, up-to-date reporting and analysis, enabling better forecasting and budgeting.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex Supported a Financial Firm's Compliance Monitoring",
    image: CaseOne?.src,
    content: `Regulatory updates extracted and flagged with solevant changes, helping the firm avoid costly compliance breaches and stay audit-ready.`,
  },
  {
    id: 2,
    title:
      "How Telex can Enable Sales Teams to Build a Qualified Lead List Faster",
    image: CaseTwo.src,
    content: `Extract and enrich contact data from multiple platforms, reducing lead generation time and improving outreach success rates.`,
  },
  {
    id: 3,
    title: "How Telex Improved Customer Sentiment Analysis for a Tech Company",
    image: CaseOne.src,
    content: `By gathering and analyzing social media and review data, the company identified product pain points early and increased customer satisfaction scores by 15%.`,
  },
];

const technologies = [
  {
    id: 1,
    title: "Handle Diverse Data Types",
    content:
      "Agents can process all kinds of data, whether it's structured tables, unstructured text, or semi-structured formats like JSON and XML.",
    link: "/technology/application-monitoring/monitor-web-application",
  },
  {
    id: 2,
    title: "Compliance and Reporting",
    content:
      "Stay compliant by continuously tracking relevant regulatory data and generating audit-ready reports on demand.",
    link: "/technology/application-monitoring/cloud-infrastructure",
  },
  {
    id: 3,
    title: "Automate Data Collection",
    content:
      "Automatically gather data from websites, databases, APis, and other sources, avoiding tedious manual extraction and speeding up workflows.",
    link: "/technology/application-monitoring/mobile-application",
  },
  {
    id: 4,
    title: "Fast Decision Making",
    content:
      "With fast, accurate data at your fingertips, your team can identify trends, spot opportunities, and react proactively to changes.",
    link: "/technology/application-monitoring/enterprise-monitoring",
  },
  {
    id: 5,
    title: "Speed Up Analysis",
    content:
      "Agents transform raw data into meaningful summaries and visualizations quickly, so you can make informed decisions without waiting for manual processing.",
    link: "/technology/application-monitoring/microservices",
  },
  {
    id: 6,
    title: "Integrate with Your Tools",
    content:
      "Agents connect seamlessly with popular databases, BI tools, CRMs, and other platforms, fitting smoothly into your existing workflows.",
    link: "/technology/application-monitoring/api-monitoring",
  },
];

const dataExtractionAndAnalysisFaq = [
  {
    id: 1,
    question: "What is Data Extraction and Analysis?",
    answer:
      "Data Extraction and Analysis is the process of automatically collecting data from various sources and transforming it into meaningful insights. Telex's platform uses AI to extract data from websites, documents, and databases, then processes it to reveal patterns and actionable intelligence.",
  },
  {
    id: 2,
    question: "What types of data can Telex extract?",
    answer:
      "Telex can extract various types of data including structured data (databases, spreadsheets), unstructured data (text documents, emails), and semi-structured data (JSON, XML). We support extraction from websites, PDFs, images, APIs, and multiple file formats.",
  },
  {
    id: 3,
    question: "How does Telex connect to my data sources?",
    answer:
      "Telex offers multiple connection methods including API integration, secure FTP, direct database connections, and web scraping capabilities. Our platform provides easy-to-use connectors for popular data sources and custom integration options for specific needs.",
  },
  {
    id: 4,
    question: "How secure is the data handled by Telex?",
    answer:
      "Telex implements enterprise-grade security measures including end-to-end encryption, secure data storage, regular security audits, and compliance with industry standards. We maintain strict data privacy protocols and offer customizable security settings to protect your sensitive information.",
  },
  {
    id: 5,
    question: "Can Telex integrate with my existing analytics tools?",
    answer:
      "Yes, Telex provides seamless integration with popular analytics tools, BI platforms, and visualization software. Our platform supports standard data export formats and offers APIs for custom integrations, allowing you to easily incorporate extracted data into your existing analytics workflow.",
  },
];

const DataExtractionAnalysis = () => {
  return (
    <>
      <Hero
        breadCumbs="Data Extraction & Analysis"
        title="Automated {{Data Extraction and Al-Powered Analysis}}"
        content="Turns messy data into clear insights, automatically extract, clean, and analyze info from websites, files, and more. No manual grind, just ready-to-use intelligence to help you move faster."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="Smart Data Extraction and Analysis Made Simple."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`Businesses face the challenge of dealing with scattered, hard-to-access data and time-consuming manual extraction. These delays in gathering and analyzing information often hold teams back from making timely, informed decisions.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Turn Your Data Into Actionable Insights"
        items={technologies}
      />
      {/* <Guides /> */}
      <Faq faq={dataExtractionAndAnalysisFaq} />
      <KeepTrack
        title="Unlock the Full Potential of Your Data With Telex"
        content="Easily extract, analyze, and act on your data — all from one intelligent platform.."
      />
    </>
  );
};

export default DataExtractionAnalysis;
