"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Customer Relationship - Telex",
    description:
      "Build stronger customer relationships with intelligent CRM tools. Enhance customer engagement and drive business growth with AI-powered insights.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/case-image-one.svg";
import CaseTwo from "../_assets/case-image-two.svg";
import CardSlider from "../components/single-product/CardSlider";

const firstData = [
  {
    id: 1,
    title: "Al support that knows your customers by name and intent",
    content: `Telex agents understand each customer's past interactions, tone, and needs to deliver context-aware responses that build loyalty.`,
  },
  {
    id: 2,
    title: "Telex agents craft thoughtful, human-like replies that resonate",
    content: `Your customer relationships are nurtured through tailored conversations that reflect empathy and precision.`,
  },
  {
    id: 3,
    title: "Learns from each conversation and adapts your support tone",
    content: `The system evolves with every message, refining its responses to match how you want your brand to sound.`,
  },
  {
    id: 4,
    title: "Human in the loop",
    content: `Your business matters. Telex agents only act when they are sure, and will ask for your input when needed.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex transformed customer engagement in retail",
    image: CaseOne?.src,
    content: `Retail teams centralized conversations and streamlined communication across channels, leading to faster resolutions and stronger relationships.`,
  },
  {
    id: 2,
    title: "How real estate teams built better client trust with Telex",
    image: CaseTwo.src,
    content: `By automating follow-ups and keeping every interaction documented, agents stayed responsive and clients felt consistently supported.`,
  },
  {
    id: 3,
    title: "How Telex helps teams deepen loyalty without scaling headcount",
    image: CaseOne.src,
    content: `With intelligent workflows and customer history at their fingertips, businesses deliver a personal touch, without the overhead.`,
  },
];

const technologies = [
  {
    id: 1,
    title: "Al-Powered Customer Conversations",
    content:
      "Telex agents handle inquiries, follow-ups, and routine messages, trained on your tone and customer history to deliver consistent, empathetic responses.",
    link: "/technology/application-monitoring/monitor-web-application",
  },
  {
    id: 2,
    title: "Add Brand Personality to Every Interaction",
    content:
      "Telex agents learn your brand voice and audience preferences, ensuring every message reflects your values and builds trust.",
    link: "/technology/application-monitoring/cloud-infrastructure",
  },
  {
    id: 3,
    title: "Design Multi-Step Engagement Pipelines That Scale",
    content:
      "From onboarding to retention campaigns, build workflows that automate repetitive communication and surface human input where needed.",
    link: "/technology/application-monitoring/mobile-application",
  },
  {
    id: 4,
    title: "Keep Human Review Where It Matters Most",
    content:
      "Let agents handle FAQs and updates while your team focuses on resolving complex requests and building stronger relationships.",
    link: "/technology/application-monitoring/enterprise-monitoring",
  },
  {
    id: 5,
    title: "Track Sentiment and Conversation Outcomes",
    content:
      "Monitor support threads, detect tone shifts, and optimize messaging based on real feedback from your users.",
    link: "/technology/application-monitoring/microservices",
  },
  {
    id: 6,
    title: "Multi-Channel Customer Engagement",
    content:
      "Communicate via email, chat, and SMS, all from one platform. Telex agents adapt your message for the right channel and audience.",
    link: "/technology/application-monitoring/api-monitoring",
  },
];

const slides = [
  {
    title: "Set Up Telex",
    description:
      "Sign up on Telex and set up your customer engagement workspace.",
    button: {
      text: "Sign up Now",
      href: "/auth/sign-up",
    },
  },
  {
    title: "Set Up Your Communication Channels in Telex",
    description:
      "Connect your email, chat, and messaging platforms in Telex to manage customer interactions, follow-ups, and feedback in one place.",
  },
  {
    title: "Track, Respond, and Manage Relationships with Ease",
    description:
      "Handle inquiries, automate responses, and maintain a full history of conversations, all from your Telex dashboard.",
  },
];

const customerRelationshipFAQ = [
  {
    id: 1,
    question: "What is Telex?",
    answer:
      "Telex is an Al-powered workflow automation platform that helps teams streamline tasks like content creation, invoice processing, lead generation, document management, and more, all from one place.",
  },
  {
    id: 2,
    question: "How does Telex help with Customer Relationship Management?",
    answer:
      "Telex keeps your team aligned on customer conversations by tracking tasks like follow-ups, issue resolutions, escalations, and client onboarding, ensuring no request goes unanswered.",
  },
  {
    id: 3,
    question: "How easy is it to set up?",
    answer:
      "Setup is simple. Create an account, organize your customer engagement workflows as channels, and begin receiving real-time updates on messages, inquiries, or ticket status.",
  },
  {
    id: 4,
    question: "How much does it cost?",
    answer:
      "Telex offers a free tier for individuals and small teams. For larger support teams with more customer channels and advanced workflows, pricing is usage-based. See our pricing page for details.",
  },
  {
    id: 5,
    question: "Can it use my own context and data to handle queries?",
    answer:
      "Yes. Telex can ingest customer support scripts, onboarding checklists, and client preferences to trigger relevant alerts and reminders tailored to your team's communication strategy.",
  },
];

const CustomerRelationship = () => {
  return (
    <>
      <Hero
        breadCumbs="Customer Relationship"
        title="Build {{Stronger Customer Relationships}} Without the Busywork"
        content="From onboarding to retention, let Telex Al agents automate follow-ups, updates, and engagement, so your team can focus on building loyalty, not chasing tasks"
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="Make your customers feel heard, understood, and valued, automatically."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`We have deployed Telex across many industries like e-commerce, banking, and healthcare, and consistently seen faster response times, improved customer satisfaction, and stronger engagement across the board.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Build Automated and Semi-Automated Customer Engagement Workflows"
        items={technologies}
      />
      <div className="relative px-4 md:px-6 overflow-hidden">
        <div className="max-w-7xl mx-auto py-[60px]">
          <div className="flex justify-between items-center mb-10">
            <button className="px-4 py-1 text-sm font-semibold text-gray-900 border border-gray-400 bg-[#F9FAFB] rounded-full hover:bg-gray-100">
              <span className="text-gray-700">✩</span> <span>Guides</span>
            </button>
          </div>
          <CardSlider slides={slides} />
        </div>
      </div>
      <Faq faq={customerRelationshipFAQ} />
      <KeepTrack
        title="Al-powered customer engagement using the powerful Telex Communication Control Center"
        content="Easy to use, but fully extensible communication management powered by Al agents. Spend a day setting it up and enjoy a lifetime of coordinated, high-quality customer engagement workflows, with automation that scales as you grow."
      />
    </>
  );
};

export default CustomerRelationship;
