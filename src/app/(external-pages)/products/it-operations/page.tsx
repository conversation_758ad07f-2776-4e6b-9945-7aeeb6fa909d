"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "IT Operations - Telex",
    description:
      "Enhance IT efficiency with automated operational solutions. Streamline IT processes, monitor systems, and optimize performance with intelligent automation.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import <PERSON> from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/case-image-one.svg";
import CaseTwo from "../_assets/case-image-two.svg";

const firstData = [
  {
    id: 1,
    title: "Analyse Network Performance With AI",
    content: `Continuously analyze network performance metrics like latency, packet loss, and throughput across global office locations. One Al agent keeps a real-time pulse on all network links, flags degradations, and suggests rerouting options or root causes before users are affected.`,
  },
  {
    id: 2,
    title: "Security and Compliance Check Automation",
    content: `Run scheduled Al audits to catch outdated software, misconfigurations, or missing patches, so your systems stay secure and compliant.`,
  },
  {
    id: 3,
    title: "Log Intelligence and Error Pattern Detection",
    content: `Telex agent scans logs across your systems, groups recurring errors, and flags unusual behavior, helping you spot root causes fast, without manual digging.`,
  },
  {
    id: 4,
    title: "Internal System Health Monitoring with Telex",
    content: `Agents track your key metrics; load, response time, dependencies, and only alert when something's truly off, cutting down on noise and alert fatigue.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex Resolved Network Outages for a Retail Chain",
    image: CaseOne?.src,
    content: `Telex tracked real-time network traffic and pinpointed unusual packet loss across branches. The team fixed a faulty switch and reduced network downtime by 60%.`,
  },
  {
    id: 2,
    title: "How Telex Helped a Bank Improve Internal System Reliability",
    image: CaseTwo.src,
    content: `By monitoring app usage across departments and catching CPU spikes early, we helped IT staff prevent bottlenecks in core systems used for loan processing.`,
  },
  {
    id: 3,
    title: "How Telex Improved Software Rollouts Across Branch Locations",
    image: CaseOne.src,
    content: `With real-time feedback on app behavior post-deployment, We helped an insurance provider detect failed installs across branches and roll out fixes remotely.`,
  },
];

const technologies = [
  {
    id: 1,
    title: "Network Tracking",
    content:
      "Keep tabs on internal and external networks, detect connectivity issues early, and get insights into traffic flow and latency across your infrastructure.",
    link: "/technology/application-monitoring/monitor-web-application",
  },
  {
    id: 2,
    title: "Incident Response",
    content:
      "Respond quickly when things go wrong. Automatically escalate critical issues, route them to the right teams, and track resolution timelines.",
    link: "/technology/application-monitoring/cloud-infrastructure",
  },
  {
    id: 3,
    title: "System Health Visibility",
    content:
      "Get a clear view of all your systems; servers, databases, services, so you can spot performance dips, usage spikes, and potential risks in real time.",
    link: "/technology/application-monitoring/mobile-application",
  },
  {
    id: 4,
    title: "Compliance Awareness",
    content:
      "Keep records of key operational data and flag issues that could violate compliance or internal policies without manual effort.",
    link: "/technology/application-monitoring/enterprise-monitoring",
  },
  {
    id: 5,
    title: "Performance Analysis",
    content:
      "Measure and analyze how different parts of your infrastructure perform over time and get the data you need to fix bottlenecks and optimize usage.",
    link: "/technology/application-monitoring/microservices",
  },
  {
    id: 6,
    title: "Infrastructure Optimization",
    content:
      "Find underused resources, outdated components, or misconfigured systems. Telex helps you streamline your setup and cut waste.",
    link: "/technology/application-monitoring/api-monitoring",
  },
];

const itOperationFaq = [
  {
    id: 1,
    question: "What exactly does Telex help with in IT Operations?",
    answer:
      "Telex helps automate and streamline various IT operations including network performance monitoring, security compliance checks, log analysis, system health monitoring, and incident response. Our AI-powered platform helps identify and resolve issues before they impact your business operations.",
  },
  {
    id: 2,
    question: "Is Telex only for large IT teams or can small teams use it too?",
    answer:
      "Telex is designed to be scalable and suitable for teams of all sizes. Small teams can benefit from our automated monitoring and issue detection, while larger organizations can leverage our enterprise-grade features for complex infrastructure management.",
  },
  {
    id: 3,
    question: "Can I track both cloud and on-prem infrastructure?",
    answer:
      "Yes, Telex provides comprehensive monitoring capabilities for both cloud and on-premises infrastructure. Our platform seamlessly integrates with various environments, giving you unified visibility and control across your entire IT infrastructure.",
  },
  {
    id: 4,
    question: "Do I need to install anything to start using Telex?",
    answer:
      "Telex is primarily cloud-based and requires minimal installation. You'll only need to install lightweight agents on systems you want to monitor. Most features are accessible through our web interface, making it easy to get started quickly.",
  },
  {
    id: 5,
    question: "How does Telex help during an outage or critical issue?",
    answer:
      "During critical issues, Telex provides real-time alerts, automated incident response, and detailed diagnostics. Our AI helps identify root causes quickly, suggests remediation steps, and maintains communication channels for faster resolution of outages.",
  },
];

const ITOperations = () => {
  return (
    <>
      <Hero
        breadCumbs="IT Operations"
        title="Use AI to {{Stay on Top}} of Your IT Operations"
        content="Keep your networks, internal systems, and infrastructure running smoothly with Al agents that watch for issues, troubleshoot in real time, and escalate only when needed, so your team stays focused and productive."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="Put AI to Work Across Your IT Infrastructure"
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`IT operations shouldn't feel like firefighting. With Telex, teams stay ahead of system issues, spot weak points early, and keep networks and internal tools running smoothly without drowning in noise.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Get Full Control Over Your IT Stacks"
        items={technologies}
      />
      {/* <Guides /> */}
      <Faq faq={itOperationFaq} />
      <KeepTrack
        title="Use Telex to Run Smarter, Safer IT Operations with Al on Your Side"
        content="From network performance to internal systems and security, We give your team the visibility, automation, and insights they need to stay ahead of issues and keep operations running smoothly."
      />
    </>
  );
};

export default ITOperations;
