import React from "react";
import {
  Accordion,
  AccordionItem,
  Accordion<PERSON>ontent,
  AccordionTrigger,
} from "~/components/ui/accordion";
import Image from "next/image";
import { CircleArrowDown } from "lucide-react";

const Faq: React.FC = () => {
  return (
    <div className="mx-auto max-w-[1240px] gap-[40px] flex flex-col md:flex-row justify-center items-center">
      <div className="w-full md:w-[50%]">
        <div className="hidden md:block rounded-full overflow-hidden mx-auto w-full">
          <Image
            src="/images/faq-image.jpg"
            alt="FAQ Image"
            width={457}
            height={457}
            className="object-cover w-full h-full"
          />
        </div>
      </div>
      <div className="w-full flex items-start justify-center flex-col mx-6 md:w-[50%] sm:w-[500px] lg:w-[676px]">
        <h1 className="text-3xl font-semibold mb-4  w-full lg:w-auto lg:text-start">
          Got Questions?
        </h1>
        <p className="mb-8 lg:text-start w-full lg:w-auto">
          Explore common questions and answers to get the most out of Telex.
        </p>

        <div className="block mx-auto mb-2 md:hidden lg:hidden ">
          <div className="rounded-full overflow-hidden w-[250px] h-[250px]">
            <Image
              src="/images/faq-image.jpg"
              alt="FAQ Image"
              width={250}
              height={250}
              className="object-cover w-full h-full"
            />
          </div>
        </div>

        <div className="w-full text-left">
          <Accordion type="single" defaultValue="item-1" collapsible>
            <AccordionItem value="item-1">
              <AccordionTrigger className="text-[#1D1D1D] py-8">
                What is Telex?
                <CircleArrowDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
              </AccordionTrigger>

              <AccordionContent className="text-[#767676]">
                Telex is an Al-powered workflow automation platform that helps
                teams streamline tasks like content creation, invoice
                processing, lead generation, document management, and more, all
                from one place.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-2">
              <AccordionTrigger className="text-[#1D1D1D] py-8">
                How can I use Telex for my team?
                <CircleArrowDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
              </AccordionTrigger>

              <AccordionContent className="text-[#767676]">
                You can organize workflows by creating dedicated channels for
                specific teams or use cases (e.g., marketing, devops, finance).
                Within each channel, you can activate Al agents tailored to your
                team's goals.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-3">
              <AccordionTrigger className="text-[#1D1D1D] py-8">
                Can I integrate Telex with my existing tools?
                <CircleArrowDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
              </AccordionTrigger>

              <AccordionContent className="text-[#767676]">
                Yes. Telex integrates with tools like Google Docs, Slack, Gmail,
                Trello, Notion, GitHub, and more. These integrations enable Al
                agents to work within your current systems and simplify your
                operations.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-4">
              <AccordionTrigger className="text-[#1D1D1D] py-8">
                What tasks can Telex automate?
                <CircleArrowDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
              </AccordionTrigger>

              <AccordionContent className="text-[#767676]">
                Telex can automate content creation, invoice processing,
                document analysis, data extraction, customer support, email
                marketing, lead nurturing, and more. Just choose a workflow, and
                activate an Al agent.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-5">
              <AccordionTrigger className="text-[#1D1D1D] py-8">
                Can I customize channels and agents?
                <CircleArrowDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
              </AccordionTrigger>

              <AccordionContent className="text-[#767676]">
                Absolutely. You can personalize each Al agent to meet your
                business needs, adjust inputs, select the right tools, and
                define how the agent behaves in each workflow.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </div>
  );
};

export default Faq;
